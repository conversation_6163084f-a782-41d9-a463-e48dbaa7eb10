import React from "react";

import ReviewFilters from "../ReviewFilters";
import RevieweeSummarySidebarContainer from "containers/PerformanceReviews/pages/ReviewFormBetter/components/global-components/RevieweeSummarySidebarContainer";
import { SecondaryButtonNewMedium } from "DesignSystem/Buttons";
import CommentSidebar from "./components/CommentSidebar";
import ManageQuestions from "./components/ManageQuestions";
import CalibrationAnalytics from "./components/CalibrationAnalytics";
import { Input } from "DesignSystem/Input";
import {
  Table,
  ConfigProvider,
  Empty,
  Tooltip,
  Dropdown,
  Switch,
  Spin,
  Menu,
} from "antd";
import {
  DownOutlined,
  EditOutlined,
  LoadingOutlined,
  SearchOutlined,
} from "@ant-design/icons";

import Arrow from "resources/images/left-arrow.svg";

import CalibrationZeroStateImage from "resources/images/zeroStatesImages/calibrationZeroState.svg";

import cx from "classnames";

import {
  noQuestionZeroStateText,
  noRevieweeForCalibrationText,
} from "constants/review.constants";
import useCalibrationTab from "./hooks/useCalibrationTab";

import "./index.scss";
import CSVBoxWrapper from "components/CSVBoxWrapper";
import { Modal } from "DesignSystem/Modal";
import { Loader } from "components/Loader";
import CalibrationInfoBanner from "./components/CalibrationInfoBanner";

const CalibrationTab = ({ reviewCycleDetails }) => {
  const {
    showZeroState,
    showTableZeroState,
    isBannerVisible,
    setIsBannerVisible,
    noQuestionZeroState,
    searchValue,
    setSearchValue,
    filterKeys,
    appliedFilters,
    debouncedSearchQuery,
    setAppliedFilters,
    handleDownloadCSV,
    isManageQuestionsDropdownVisible,
    setIsManageQuestionsDropdownVisible,
    tableLoading,
    columns,
    tableData,
    calibrationTableData,
    paginationText,
    handleNextPage,
    handlePreviousPage,
    selectedRevieweeForComment,
    generateParamStringForCalibrationTable,
    setSelectedRevieweeForComment,
    selectedRevieweeForSummary,
    calibrationData,
    setSelectedRevieweeForSummary,
    showAnalytics,
    setShowAnalytics,
    questionsForAnalytics,
    selectedQuestionForAnalytics,
    setSelectedQuestionForAnalytics,

    analyticsData,
    showCSVDropdown,
    setShowCSVDropdown,
    csvBoxConfig,
    showLoaderForCSVBox,
    setShowLoaderForCSVBox,
    handleImportCSVSuccess,
    update9Box,
    setUpdate9Box,
    chartType,
    setChartType,
    fetchCSVConfig,
    // Value type props
    valueTypeConfig,
    valueType,

    handleValueTypeSelection,
  } = useCalibrationTab();

  // main return
  return (
    <div className="w-100 position-relative flex justify-center calibration-tab-container">
      <div
        className={cx("w-100 flex flex-col  calibration-tab", {
          "calibration-tab-with-analytics": showAnalytics,
          "calibration-tab-without-analytics": !showAnalytics,
        })}
      >
        {!showZeroState || noQuestionZeroState ? (
          <div className="mx-24 mt-24 mb-12 flex justify-between items-center calibration-actions">
            <div className="flex align-center calibration-table-functions">
              <Input
                className="calibration-search-input mr-20"
                value={searchValue}
                onChange={(e) => {
                  setSearchValue(e.target.value);
                }}
                prefix={<SearchOutlined />}
                placeholder={"Type to search for reviewee"}
              />
              <ReviewFilters
                filterKeys={filterKeys}
                appliedFilters={appliedFilters}
                setAppliedFilters={setAppliedFilters}
              />
            </div>
            <div className="flex align-center show-analytics-switch-container">
              <div className="flex align-center mr-20">
                <span className="mr-10 fs-medium fw-500">Show Analytics</span>
                <Switch
                  checked={showAnalytics}
                  onChange={(checked) => {
                    setShowAnalytics(checked);
                    if (chartType === null) {
                      setChartType("bar");
                    }
                  }}
                />
              </div>
              <Dropdown
                trigger={["click"]}
                onVisibleChange={(open) => {
                  setShowCSVDropdown(open);
                  open && fetchCSVConfig();
                }}
                overlay={
                  <Menu className="calibration-csv-action-menu">
                    <Menu.Item>
                      <CSVBoxWrapper
                        config={csvBoxConfig}
                        LauncherComponent={LauncherComponent}
                        lazy={true}
                        useOptions={true}
                        loadStarted={() => {
                          if (setShowLoaderForCSVBox)
                            setShowLoaderForCSVBox(true);
                        }}
                        onReady={() => {
                          if (setShowLoaderForCSVBox)
                            setShowLoaderForCSVBox(false);
                        }}
                        onSuccess={handleImportCSVSuccess}
                      />
                    </Menu.Item>
                    <Menu.Item onClick={handleDownloadCSV}>
                      Export CSV
                    </Menu.Item>
                  </Menu>
                }
              >
                <SecondaryButtonNewMedium
                  className={cx("mr-20 csv-dropdown-button", {
                    "opacity-05 disabled": showZeroState,
                  })}
                  disabled={showZeroState}
                >
                  <div className="fs-medium flex align-center">
                    <span>CSV</span>
                    <DownOutlined
                      className={cx("ml-5", {
                        "csv-dropdown-arrow-down": !showCSVDropdown,
                        "csv-dropdown-arrow-up": showCSVDropdown,
                      })}
                    />
                  </div>
                </SecondaryButtonNewMedium>
              </Dropdown>
              <Tooltip title="Export Current View"></Tooltip>
              <Dropdown
                visible={isManageQuestionsDropdownVisible}
                overlay={
                  <ManageQuestions
                    close={() => setIsManageQuestionsDropdownVisible(false)}
                    filterParams={generateParamStringForCalibrationTable()}
                  />
                }
                placement="bottomRight"
                trigger="click"
              >
                <SecondaryButtonNewMedium
                  onClick={() =>
                    setIsManageQuestionsDropdownVisible(
                      !isManageQuestionsDropdownVisible
                    )
                  }
                  className="flex align-center manage-question-btn"
                >
                  <div className="flex align-center fs-medium manage-question-btn-content">
                    <EditOutlined className="mr-5 manage-question-icon" />
                    <span className="manage-question-btn-text">
                      Edit Columns
                    </span>
                  </div>
                </SecondaryButtonNewMedium>
              </Dropdown>
            </div>
          </div>
        ) : null}
        <CSVBoxLoader
          showLoader={showLoaderForCSVBox}
          onClose={() => {
            // setShowLoaderForCSVBox(false);
          }}
        />

        {isBannerVisible && (
          <CalibrationInfoBanner
            setIsBannerVisible={setIsBannerVisible}
            showAnalytics={showAnalytics}
            setShowAnalytics={setShowAnalytics}
            isManageQuestionsDropdownVisible={isManageQuestionsDropdownVisible}
            setIsManageQuestionsDropdownVisible={
              setIsManageQuestionsDropdownVisible
            }
          />
        )}

        {showAnalytics && (
          <CalibrationAnalytics
            reviewCycleDetails={reviewCycleDetails}
            selectedQuestionForAnalytics={selectedQuestionForAnalytics}
            setSelectedQuestionForAnalytics={setSelectedQuestionForAnalytics}
            questionsForAnalytics={questionsForAnalytics}
            valueTypeConfig={valueTypeConfig}
            handleValueTypeSelection={handleValueTypeSelection}
            valueType={valueType}
            analyticsData={analyticsData}
            appliedFilters={appliedFilters}
            update9Box={update9Box}
            setUpdate9Box={setUpdate9Box}
            chartType={chartType}
            setChartType={setChartType}
          />
        )}
        <div className="w-100 calibration-table-container">
          {showTableZeroState ? (
            <CalibrationZeroState noQuestionZeroState={noQuestionZeroState} />
          ) : (
            <ConfigProvider
              renderEmpty={() =>
                tableLoading ? null : (
                  <Empty
                    description={
                      appliedFilters?.length || debouncedSearchQuery?.length
                        ? "No reviewee to show for applied filters"
                        : "No reviewees"
                    }
                  />
                )
              }
            >
              <Table
                className={cx("w-100", {
                  "calibration-table-loading": tableLoading,
                  "calibration-table": !tableLoading,
                })}
                sticky
                columns={columns}
                dataSource={tableData}
                bordered={true}
                pagination={false}
                loading={{
                  spinning: tableLoading,
                  indicator: <Loader />,
                }}
                scroll={{ x: 800 }}
              />
            </ConfigProvider>
          )}
        </div>
        {!!calibrationTableData?.pagination?.total_entries && !showZeroState && (
          <div className="flex align-center w-100 justify-end px-24 py-8 pagination">
            <span>{paginationText}</span>
            <div
              className={cx(
                "flex align-center justify-center ml-5 mr-5 page-change-button",
                {
                  "cursor-pointer":
                    calibrationTableData?.pagination?.previous_page,
                  "cursor-not-allowed":
                    !calibrationTableData?.pagination?.previous_page,
                }
              )}
              onClick={handlePreviousPage}
            >
              <img src={Arrow} height={"12px"} width={"12px"} />
            </div>
            <div
              className={cx(
                "flex align-center justify-center ml-5  page-change-button rotate-arrow",
                {
                  "cursor-pointer": calibrationTableData?.pagination?.next_page,
                  "cursor-not-allowed":
                    !calibrationTableData?.pagination?.next_page,
                }
              )}
              onClick={handleNextPage}
            >
              <img src={Arrow} height={"12px"} width={"12px"} />
            </div>
          </div>
        )}
      </div>
      {selectedRevieweeForComment && (
        <CommentSidebar
          reviewee={selectedRevieweeForComment}
          additionalParamString={generateParamStringForCalibrationTable()}
          closeSidebar={() => setSelectedRevieweeForComment("")}
        />
      )}
      <RevieweeSummarySidebarContainer
        revieweeId={selectedRevieweeForSummary}
        revieweeName={
          calibrationData?.reviewees?.[selectedRevieweeForSummary]?.reviewee
            ?.name
        }
        close={() => setSelectedRevieweeForSummary(null)}
        pageOrigin={"admin_right_panel"}
      />
    </div>
  );
};

const CalibrationZeroState = ({ noQuestionZeroState }) => {
  return (
    <div className="flex flex-col align-center justify-center w-100 h-100">
      <img src={CalibrationZeroStateImage} width={"320px"} height={"220px"} />

      {noQuestionZeroState ? (
        <span
          className="fw-600 fs-default text-center align-center mt-10"
          style={{ width: "320px" }}
        >
          {noQuestionZeroStateText}
        </span>
      ) : (
        <span
          className="fw-600 fs-default text-center align-center mt-10"
          style={{ width: "320px" }}
        >
          {noRevieweeForCalibrationText}
        </span>
      )}
    </div>
  );
};

const LauncherComponent = ({ isLoading, launch, templateLink }) => {
  return (
    <div
      className={cx("flex align-center justify-start", {
        "cursor-not-allowed": isLoading,
        "cursor-pointer": !isLoading,
      })}
      onClick={(event) => {
        if (!isLoading) {
          launch();
        }
        event.stopPropagation();
      }}
    >
      <span className="fs-16 color-grey">Import from CSV</span>
      {isLoading && (
        <Spin
          indicator={
            <LoadingOutlined
              className="color-primary fs-default ml-10"
              style={{ fontSize: 16 }}
              spin
            />
          }
        />
      )}
    </div>
  );
};

const CSVBoxLoader = ({ showLoader, onClose }) => {
  return (
    <Modal
      visible={showLoader}
      onCancel={onClose}
      footer={null}
      maskClosable={false}
      className="csv-box-modal-loader"
    >
      <div className="flex align-center justify-center">
        <Loader containerClassName="csv-box-loader" />
      </div>
    </Modal>
  );
};

export default CalibrationTab;
