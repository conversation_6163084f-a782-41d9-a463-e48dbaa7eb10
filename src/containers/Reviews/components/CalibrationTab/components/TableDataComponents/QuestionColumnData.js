import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import cx from "classnames";
import { EditOutlined } from "@ant-design/icons";

import { Input } from "DesignSystem/Input";
import { Tooltip } from "DesignSystem/Tooltip";
import ConfidentialScoreDiv from "./ConfidentialScoreDiv";
import CustomTextColumn from "./CustomTextColumn";

import { isCssEllipsisAppliedWithWidth } from "constants/global.constants";
import useDebounce from "containers/Goals/components/CheckInModal/debounce";

const QuestionColumnData = ({
  uniqueId,
  revieweeId,
  rows,
  column,
  editable,
  handleCustomColumnScoreChange,
  getScoreCellColorClass,
  isConfidential,
}) => {
  const currentBlock = rows?.[column?.id];
  const response = currentBlock?.values[0]?.value;
  const isTextResponse = column.question?.question_type === "text";
  const isClipped = useRef();

  const value = useMemo(() => {
    if (!isTextResponse) {
      return response;
    }

    return response?.length > 30 ? `${response?.slice(0, 30)}...` : response;
  }, [response, isTextResponse]);

  const tooltipTitle = useMemo(() => {
    if (isTextResponse) {
      return;
    }

    return currentBlock?.values?.[0]?.description || (isNaN(value) && value);
  }, [isTextResponse]);

  const [showCustomColumnInput, setShowCustomColumnInput] = useState(false);
  const [showHoverText, setShowHoverText] = useState(false);
  const [customColumnScore, setCustomColumnScore] = useState(
    isTextResponse ? currentBlock?.values[0]?.html_content : response
  );
  const customColumnScoreRef = useRef();
  const debouncedScore = useDebounce(customColumnScore, 700);

  useEffect(() => {
    const ele = document.getElementById(uniqueId);
    if (ele) {
      isClipped.current = isCssEllipsisAppliedWithWidth(ele);
    }
  }, [uniqueId]);

  const handleChange = useCallback(
    (event) => {
      let inputVal = event.target.value;

      if (!isTextResponse) {
        inputVal = inputVal.replace(/[^0-9.]/g, "");
      }

      setCustomColumnScore(inputVal);
    },
    [isTextResponse]
  );

  const saveUpdatedColumnData = useCallback(() => {
    handleCustomColumnScoreChange(
      debouncedScore,
      value,
      setCustomColumnScore,
      column?.id,
      revieweeId,
      currentBlock?.entity_id,
      isTextResponse
    );
    isTextResponse && setShowCustomColumnInput(false);
  }, [debouncedScore, value, column, revieweeId, currentBlock, isTextResponse]);

  useEffect(() => {
    if (editable && !isTextResponse && response !== debouncedScore) {
      saveUpdatedColumnData();
    }
  }, [debouncedScore]);

  useEffect(() => {
    if (showCustomColumnInput && customColumnScoreRef.current) {
      customColumnScoreRef.current.focus();
    }
  }, [showCustomColumnInput]);

  if (isConfidential) {
    return <ConfidentialScoreDiv />;
  }

  if (editable) {
    return (
      <>
        <div
          className={cx(
            "flex justify-center align-center h-100 w-100 custom-column-input-container",
            {
              "cursor-pointer": isTextResponse,
              [`${getScoreCellColorClass(parseFloat(customColumnScore))}`]:
                !showHoverText && !isTextResponse,
              "fs-small": isTextResponse,
            }
          )}
          onMouseOver={() => setShowHoverText(true)}
          onMouseLeave={() => setShowHoverText(false)}
          onKeyPress={(event) => {
            if (event.key === "Enter" && showCustomColumnInput === true) {
              setShowCustomColumnInput(false);
            }
          }}
          onClick={() => isTextResponse && setShowCustomColumnInput(true)}
        >
          {showCustomColumnInput && !isTextResponse ? (
            <Input
              ref={customColumnScoreRef}
              value={customColumnScore}
              onChange={handleChange}
              onBlur={() => !isTextResponse && setShowCustomColumnInput(false)}
              className="flex align-center fs-default fw-500 w-100 h-100 custom-column-input"
              autoFocus
            />
          ) : value !== undefined && value !== null ? (
            <>
              <div
                className={cx("fs-default fw-500 cursor-pointer", {
                  "fs-tiny": showHoverText && !isTextResponse,
                  "fs-small px-8": isTextResponse,
                })}
                onClick={() => setShowCustomColumnInput(true)}
              >
                {showHoverText && !isTextResponse
                  ? `Click to edit ${isTextResponse ? "response" : "score"}`
                  : value}
              </div>
              {isTextResponse && <EditOutlined className="show-full-text" />}
            </>
          ) : (
            <div
              className={cx(
                "fs-tiny fw-500 h-100 w-100 flex justify-center align-center",
                {
                  "cursor-pointer": showHoverText,
                  "hidden-custom-column-cell": !showHoverText,
                }
              )}
              onClick={() => setShowCustomColumnInput(true)}
            >
              {`Click to add ${isTextResponse ? "response" : "score"}`}
              {isTextResponse && <EditOutlined className="show-full-text" />}
            </div>
          )}
        </div>
        <CustomTextColumn
          saveUpdatedColumnData={saveUpdatedColumnData}
          customColumnScore={customColumnScore}
          setCustomColumnScore={setCustomColumnScore}
          close={() => {
            setCustomColumnScore(currentBlock?.values[0]?.html_content || "");
            setShowCustomColumnInput(false);
          }}
          title={column.question?.question_text}
          isOpen={showCustomColumnInput && isTextResponse}
          isEditable={editable}
        />
      </>
    );
  } else {
    return (
      <>
        <Tooltip title={isClipped.current ? tooltipTitle : ""}>
          <div
            id={uniqueId}
            className={cx(
              "position-relative px-8 py-4 flex w-100 fs-medium fw-500 align-center calibration-table-additional-column-value",
              {
                "justify-center": value?.length <= 2 || !isNaN(value),
                "justify-start": value?.length > 2,
                "fs-small px-8 cursor-pointer": isTextResponse,
              }
            )}
            onClick={() => setShowCustomColumnInput(isTextResponse)}
          >
            {/* {value?.length > 19 ? `${value?.substring(0, 15)}...` : value} */}
            {value}
            {isTextResponse && <EditOutlined className="show-full-text" />}
          </div>
        </Tooltip>
        <CustomTextColumn
          customColumnScore={currentBlock?.values[0]?.html_content}
          close={() => setShowCustomColumnInput(false)}
          title={column.question?.question_text}
          isOpen={showCustomColumnInput && isTextResponse}
          isEditable={editable}
        />
      </>
    );
  }
};

export default QuestionColumnData;
