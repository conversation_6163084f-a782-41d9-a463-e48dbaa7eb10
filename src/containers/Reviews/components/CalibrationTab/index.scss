.calibration-tab-container {
  height: 80vh;
  overflow-y: auto;

  .calibration-tab {
    max-width: 1360px;

    background-color: #ffffff;
    box-shadow: 0px 4px 9px rgba(0, 0, 0, 0.08);
    overflow-y: auto;

    .page-change-button {
      height: 24px;
      width: 24px;
      background: #ffffff;
      border: 1px solid #9799a9;
      border-radius: 3px;
    }
    .rotate-arrow {
      transform: rotate(180deg);
    }
    .calibration-actions {
      .calibration-search-input {
        width: 250px !important;
        background-color: #ffffff !important;
        border: 1px solid #d7d7e9 !important;
        border-radius: 5px !important;
        box-shadow: none !important;
        .ant-input {
          background: #ffffff !important;
        }
      }
      .calibration-search-input:focus-within {
        border: 1px solid #4652b7 !important;
      }
      .show-analytics-switch-container {
        .ant-switch-checked {
          background-color: #4652b7;
        }

        .csv-dropdown-button {
          background: linear-gradient(180deg, #fefefe 2.13%, #f7f7ff);

          &.disabled {
            pointer-events: none;
          }
        }
        .csv-dropdown-arrow-down {
          font-size: 14px;
          color: #535755;
          transition: 0.5s;
        }
        .csv-dropdown-arrow-up {
          font-size: 14px;
          color: #535755;
          transform: rotate(-180deg);
          transition: 0.5s;
        }

        .manage-question-btn {
          background: linear-gradient(180deg, #fefefe 2.13%, #f7f7ff);

          .manage-question-icon {
            height: 14px;
            margin-bottom: 2px;
          }
        }
      }
    }

    .calibration-analytics-container {
      border-radius: var(--score, 5px);
      border: 1px solid #f0f0f0;
      background: #fbfbfb;

      .three-dot-chart-menu {
        transform: rotate(90deg);
        cursor: pointer;
      }
      .three-dot-chart-menu-hidden {
        visibility: hidden;
      }
      .analytics-question-select {
        width: 400px;
        .ant-select-selector {
          padding: 0px 25px 0px 11px;
        }
        font-size: 14px !important;
        .ant-select-selector {
          font-size: 14px !important;
        }
      }
      .analytics-step-select {
        width: 78px;
        font-size: 14px !important;
        .ant-select-selector {
          font-size: 14px !important;
        }
      }

      .calibration-charts-radio-buttons {
        display: flex;
        align-items: center;
        align-self: flex-end;
        box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.15);

        .radioButton {
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 130px;
        }

        .ant-radio-button-wrapper:hover {
          background-color: #f3f7ff;
          color: #4652b7;
          border-color: #4652b7;
        }

        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          background-color: #f3f7ff;
          border-color: #4652b7;
          color: #4652b7;
        }

        .ant-radio-button-wrapper:first-child {
          border-radius: 5px 0px 0px 5px;
        }

        .ant-radio-button-wrapper:last-child {
          border-radius: 0px 5px 5px 0px;
        }

        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
          background-color: #4652b7;
        }
      }

      .x-axis-9-box,
      .y-axis-9-box {
        width: 400px;
        .ant-select-selector {
          padding: 0px 25px 0px 11px;
        }
        font-size: 14px !important;
        .ant-select-selector {
          font-size: 14px !important;
        }
      }
      .calibration-circle {
        height: 20px;
        width: 20px;
        background-color: #4652b7;
        border-radius: 50%;
      }
      .manager-circle {
        height: 20px;
        width: 20px;
        background-color: #5cb475;
        border-radius: 50%;
      }
      .ml-100 {
        margin-left: 100px;
      }

      .bar-chart-btn,
      .line-chart-btn,
      .bar-chart-btn-active,
      .line-chart-btn-active,
      .calibration-9-box-btn,
      .calibration-9-box-btn-active {
        height: 40px;
        width: 128px;
        border: 1px solid #d9d9d9;
        box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.15);
      }

      .bar-chart-btn {
        border-radius: 5px 0px 0px 5px;
        background: #fff;

        &:hover {
          border: 1px solid #4652b7;
          background: #f3f7ff;
          color: #4652b7;
        }

        &.active {
          border: 1px solid #4652b7;
          background: #f3f7ff;
          color: #4652b7;
        }
      }

      .line-chart-btn {
        border-radius: 0px;
        background: #fff;
        border-left: 0;
        border-right: 0;

        &:hover {
          border: 1px solid #4652b7;
          background: #f3f7ff;
          color: #4652b7;
        }

        &.active {
          border: 1px solid #4652b7;
          background: #f3f7ff;
          color: #4652b7;
        }
      }

      .calibration-9-box-btn {
        border-radius: 0px 5px 5px 0px;
        background: #fff;

        &:hover {
          border: 1px solid #4652b7;
          background: #f3f7ff;
          color: #4652b7;
          .calibration-9-box-icon {
            path {
              fill: #4652b7;
            }
          }
        }

        &.active {
          border: 1px solid #4652b7;
          background: #f3f7ff;
          color: #4652b7;
          .calibration-9-box-icon {
            path {
              fill: #4652b7;
            }
          }
        }
      }

      .vertical-separator {
        height: 15px;
        width: 1px;
        border: 0.5px solid;
      }
      .overview-score-container {
        border-radius: 3px;
        border: 1px solid var(--stroke-deactivated, #d7d7e9);
        background: #fff;
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.02);
        .manager-score-circle {
          height: 15px;
          width: 15px;
          background-color: #5cb475;
          border-radius: 50%;
        }
        .calibration-score-circle {
          height: 15px;
          width: 15px;
          background-color: #4652b7;
          border-radius: 50%;
        }
      }
      .calibration-9-box-loader {
        display: flex;
        align-items: center;
        margin-top: 20px;
        img {
          height: 150px !important;
          width: 150px;
        }
      }

      .value-type-info-icon {
        height: 14px;
        width: 14px;
        path {
          fill: #666666;
          stroke: #666666;
          stroke-width: 0;
        }
      }

      .help-doc-banner {
        a {
          color: inherit;
        }

        a:hover {
          text-decoration: underline;
          color: #4652b7;

          path {
            fill: #4652b7;
          }
        }

        .external-link-icon {
          height: 12px;
          width: 12px;
          path {
            fill: #666666;
          }
        }
      }
    }

    .calibration-info-banner {
      background-color: #fbf7f1;

      a {
        &:hover {
          color: #4652b7;
        }
      }
    }

    .calibration-table-container {
      max-height: 500px;
      .ant-table-ping-right:not(.ant-table-has-fix-right)
        .ant-table-container::after {
        box-shadow: none !important;
      }

      .ant-table-content {
        overflow: unset !important;
      }

      ::-webkit-scrollbar {
        -webkit-appearance: none;
        width: 10px;
        height: 10px;
      }

      ::-webkit-scrollbar-thumb {
        border-radius: 15px;
        background-color: rgba(0, 0, 0, 0.5);
        -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
      }

      .float-above {
        z-index: 2 !important;
      }

      .calibration-table {
        table-layout: fixed !important;

        .ant-table-container {
          border: none;
        }

        .ant-table-tbody {
          .ant-table-row {
            &:hover {
              .reviewee-column-actions {
                right: 16px;
                opacity: 1;
                transition: all 0.4s ease-in-out;
                pointer-events: all;
              }
            }
          }
        }
      }
      .calibration-table-loading {
        table-layout: fixed !important;
        width: 100%;
        .ant-table-container {
          border: none;
        }
      }

      .ant-table-wrapper {
        height: 100%;
        border: none;
        box-shadow: none;
        .ant-spin-nested-loading {
          height: 100%;
          .ant-spin-container {
            height: 100%;
            .ant-table {
              height: 100%;
              overflow-y: auto;
              .ant-table-placeholder {
                height: calc(544px - 103.7px);
              }
            }
            .ant-pagination-item {
              display: none;
            }
            .ant-pagination-jump-next {
              display: none;
            }
            .ant-pagination-jump-prev {
              display: none;
            }
            .ant-pagination-options {
              display: none;
            }
          }
        }
      }

      .sorted-column {
        .sort-icon {
          visibility: visible !important;
        }
      }

      .calibration-table-fixed-reviewee-column-header {
        width: 250px;

        z-index: 2;
        padding: 0px !important;
      }
      .calibration-table-fixed-reviewee-image-column-header {
        height: 40px;
        vertical-align: top;
        z-index: 2;
        padding: 0px !important;
        border-right: none !important;
      }
      .calibration-table-fixed-reviewee-column-header-title {
        height: 100%;
        width: 100%;
        padding: 16px;
        .sort-icon {
          visibility: hidden;
        }
      }
      .calibration-table-fixed-reviewee-column-header-title:hover {
        .sort-icon {
          visibility: visible;
        }
      }
      .calibration-table-fixed-manager-column-header {
        width: 180px;

        padding: 0px !important;
      }
      .calibration-table-additional-column {
        padding: 0px !important;
        border: 1px solid #ffffff;
        height: 40px;
      }
      .custom-column-input-container {
        .custom-column-input {
          text-align: center;
          border: none;
          outline: none;
          box-shadow: none;
          background: #ffffff;
          height: 40px;
          border-radius: 0px;
        }
        .custom-column-hover-text {
          .hover-text {
            display: none;
          }
          .custom-column-input {
            display: block;
          }
        }
        .hidden-custom-column-cell {
          visibility: hidden;
        }
      }

      .custom-column-hover-text:hover {
        .hover-text {
          display: block;
        }
        .custom-column-input {
          display: none;
        }
      }
      .calibration-table-additional-column-header-title {
        height: 100%;
        width: 100%;
        padding: 16px 2px 16px 16px;
        background: #f4f5f6;
        border-left: 1px solid #f0f0f0;
        .header-title-content {
          width: 80%;
          span {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
        }
        .sort-icon-container {
          .sort-icon {
            visibility: hidden;
          }
        }
      }
      .calibration-table-additional-column-header-title:hover {
        .sort-icon {
          visibility: visible;
        }
      }

      .calibration-table-additional-column-value {
        z-index: 1;
        height: 40px;
        padding: 5px 10px 5px 10px;
      }

      .calibration-table-fixed-manager-column-header-title {
        height: 100%;
        width: 100%;
        padding: 16px;
        background: #f4f5f6;
        border-left: 1px solid #f0f0f0;
        .sort-icon {
          visibility: hidden;
        }
      }
      .calibration-table-fixed-manager-column-header-title:hover {
        .sort-icon {
          visibility: visible;
        }
      }

      .calibration-table-fixed-reviewee-column {
        min-width: 249px;
        z-index: 2;
        height: 40px;
        padding: 0px 16px;

        .reviewee-column-actions {
          right: -16px;
          transition: all 0.4s ease-in-out;
          opacity: 0;
          pointer-events: none;
        }

        .hide-reviewee-text-overflow {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .download-pdf-container {
          width: 24px;
          height: 24px;
          border: 1px solid #e0e0f4;
          background: #f7f7ff;
          .download-pdf-icon {
            color: #5f5f75;
          }
          transform: rotateY(180deg);
        }
        .download-pdf-container:hover {
          background: #4652b7;
          .download-pdf-icon {
            color: #ffffff;
          }
        }
        .comment-button {
          min-width: 24px;
          height: 24px;
          border: 1px solid #e0e0f4;
          color: #5f5f75;
          padding: 3px;
          background-color: #f7f7ff;
          svg {
            path {
              stroke: #5f5f75;
            }
          }
          span {
            margin-top: 2px;
          }
        }
        .comment-button:hover {
          color: #ffffff;
          background-color: #4652b7;
          svg {
            path {
              stroke: #ffffff;
            }
          }
        }
      }
      .calibration-table-fixed-reviewee-image-column {
        width: 100px;
        z-index: 2;
        padding: 0px 10px;
        border-right: none !important;
      }
      .calibration-table-fixed-manager-column {
        width: 180px;
        z-index: 2;
        padding-left: 16px;
      }

      .calibration-table-moveable-column-parent,
      .calibration-table-moveable-column-parent-small {
        vertical-align: top;
        white-space: pre-wrap;
        text-align: left;
        z-index: 1;

        .question-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }

        .column-header-three-dots {
          display: flex;
          width: 24px;
          height: 24px;
          opacity: 0;
          transition: opacity 0.3s ease-in-out;

          svg {
            width: 14px;
            height: 14px;
          }

          &:hover {
            svg {
              color: #4652b7;
            }
          }
        }

        .question-column-header {
          &:hover {
            .column-header-three-dots {
              opacity: 1;
            }
          }
        }

        .column-header-three-dots-opened {
          opacity: 1;
        }

        .score-labels {
          .child-column-name {
            width: 90%;
          }
          .sort-icon-container {
            visibility: hidden;
            width: 10%;
            .sort-icon {
              width: 10px;
              height: 10px;
            }
          }
        }

        padding: 4px;
      }
      th.selected-calibration-column-for-analytics {
        .header-title-content {
          color: #4652b7;
        }
        .calibration-table-additional-column-header-title {
          background: #f3f7ff !important;
        }
      }
      th.calibration-table-moveable-column-parent.selected-calibration-column-for-analytics {
        background: #f3f7ff !important;
        .question-text,
        .child-column-name {
          color: #4652b7;
        }
      }
      .calibration-table-moveable-column-parent:hover,
      .calibration-table-moveable-column-parent-small:hover {
        .sort-icon-container {
          visibility: visible;
        }
      }

      .calibration-table-moveable-column-parent {
        width: 232px;
      }
      .calibration-table-moveable-column-parent-small {
        width: 116px;
      }

      .calibration-table-moveable-column-child {
        width: 114px;
        height: 40px;
        border: 1px solid #ffffff;
        padding: 0px !important;
        z-index: 1;
      }

      .calibration-table-moveable-column-child-score-header {
        padding: 12px !important;
      }

      .ant-table-thead {
        th {
          font-size: 12px !important;
          color: #333333 !important;

          background: #f4f5f6 !important;
        }
        th.calibration-table-moveable-column-child {
          display: none;
        }
      }

      .ant-table-tbody > tr > td {
        padding: 0px 16px;
      }

      .score-exceptional {
        background: #3cd2b3;
      }
      .score-highly-effective {
        background: #b8dbd7;
      }
      .score-effective {
        background: #cde4e3;
      }
      .score-minimally-effective {
        background: #ffd7ca;
      }
      .score-not-effective {
        background: #f59597;
      }
      .empty-score-cell {
        background: #f1f1f3;
        .empty-score-cell-content {
          visibility: hidden;
        }
      }
      .empty-score-cell:hover {
        .empty-score-cell-content {
          visibility: visible;
        }
      }
    }
  }
}

.text-response-box {
  // max-height: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.show-full-text {
  position: absolute;
  right: 5px;
  top: 5px;
  background-color: transparent;
  transition: background-color 0.3s ease-in-out;

  path {
    fill: transparent;
    transition: fill 0.3s ease-in-out;
  }
}

.cell-container,
.calibration-table-additional-column-value,
.custom-column-input-container {
  line-break: anywhere;

  &:hover {
    .show-full-text {
      background-color: #4652b7;

      &.disabled {
        cursor: not-allowed;
        background-color: #9799a9;
      }

      path {
        fill: #fff;
      }
    }
  }
}

.analytics-extra-actions {
  .ant-dropdown-menu-submenu-arrow {
    top: 6px;
  }
}
.three-dot-chart-menu-overlay {
  top: 345px !important;
}

.analytics-series-value-submenu {
  .ant-radio-inner::after {
    background-color: #4652b7 !important;
    border-color: transparent;
  }

  .ant-radio-inner {
    border-color: #4652b7 !important;
  }
}
.calibration-csv-action-menu {
  border-radius: var(--score, 5px);
  border: 1px solid #e5e5f3;
  background: #fff;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.05);
}
.selected-9-box-drawer {
  .ant-drawer-body {
    padding: 0px !important;
  }
  .selected-9-box-drawer-data {
    background: #fafafa;
    padding: 5px 20px 20px 20px;
    .calibration-9-box-reviewee-card {
      padding: 15px;
      background-color: #ffffff;
      border-radius: 10px;
      .reviewee-question-text {
        width: 85%;
      }
      .reviewee-question-score {
        width: 15%;
      }
    }
  }
}

@media only screen and (max-width: 1500px) {
  .calibration-tab-container {
    .calibration-tab {
      max-width: 1200px;
      .calibration-9-box-box {
        width: 350px !important;

        .box-name-input {
          width: 50%;
        }
      }
      .x-axis-9-box,
      .y-axis-9-box {
        width: 300px !important;
      }
      .x-axis-label-1 {
        left: 392px !important;
      }
      .x-axis-label-2 {
        left: 742px !important;
      }
      .x-axis-label-3 {
        left: 1090px !important;
      }
    }
  }
}
.link {
  color: inherit;
  text-decoration: none;
}

.link:hover {
  color: #4652b7;
  text-decoration: underline;
}
