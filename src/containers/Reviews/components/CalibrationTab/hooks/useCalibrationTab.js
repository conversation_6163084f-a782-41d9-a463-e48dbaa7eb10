import { useParams } from "react-router-dom";

import { useSelector, useDispatch } from "react-redux";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { WebService } from "services/web/webService";

import {
  getCalibratableQuestions,
  getCalibrationTableData,
  setSelectedCalibrationQuestion,
} from "actions/PerformanceReviews/ManageReviews";
import useDebounce from "customHooks/useDebounce";

import CalibrationScoreCell from "../components/CalibrationScoreCell";
import RevieweeImageColumn from "../components/TableDataComponents/RevieweeImageColumn";
import RevieweeColumnHeader from "../components/TableDataComponents/RevieweeColumnHeader";
import RevieweeColumnData from "../components/TableDataComponents/RevieweeColumnData";
import ManagerColumnHeader from "../components/TableDataComponents/ManagerColumnHeader";
import ManagerColumnData from "../components/TableDataComponents/ManagerColumnData";
import QuestionColumnHeader from "../components/TableDataComponents/QuestionColumnHeader";
import QuestionColumnData from "../components/TableDataComponents/QuestionColumnData";
import NotEvaluatedDiv from "../components/TableDataComponents/NotEvaluatedDiv";
import ConfidentialScoreDiv from "../components/TableDataComponents/ConfidentialScoreDiv";

import { message, notification } from "antd";

import { CheckCircleOutlined } from "@ant-design/icons";
import { fetchCSVBoxConfig } from "actions/CSVBox";
import ResponseBox from "../components/TableDataComponents/ResponseBox";
import { Tooltip } from "DesignSystem/Tooltip";

const useCalibrationTab = () => {
  // redux
  const { accountId } = useSelector((state) => state.user);
  const calibrationData = useSelector(
    (state) => state.performanceReview.manageReviews.calibration_reviewees
  );
  const csvBoxConfig = useSelector(
    (state) => state?.CSVBoxConfig?.csv_box_configs?.calibration_import
  );

  const selectedCalibrationQuestion = useSelector(
    (state) =>
      state.performanceReview.manageReviews?.selectedCalibrationQuestion
  );

  // states for reviewee search
  const [searchValue, setSearchValue] = useState("");

  // states for filters
  const [filterKeys, setFilterKeys] = useState({});
  const [appliedFilters, setAppliedFilters] = useState([]);

  // states for calibration table
  const [showZeroState, setShowZeroState] = useState(true);
  const [noQuestionZeroState, setNoQuestionZeroState] = useState(false);
  const [calibrationTableData, setCalibrationTableData] = useState();
  const [tableLoading, setTableLoading] = useState(true);
  const [tableData, setTableData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showCSVDropdown, setShowCSVDropdown] = useState(false);
  const [showLoaderForCSVBox, setShowLoaderForCSVBox] = useState(false);

  // states for sorting
  const [columnForSorting, setColumnForSorting] = useState();
  const [subColumnForSorting, setSubColumnForSorting] = useState();
  const [sortOrder, setSortOrder] = useState("no-sort");

  // states for reviewee actions
  const [selectedRevieweeForComment, setSelectedRevieweeForComment] =
    useState("");
  const [selectedRevieweeForSummary, setSelectedRevieweeForSummary] =
    useState(null);

  // states for analytics
  const [showAnalytics, setShowAnalytics] = useState(
    localStorage.showCalibrationAnalytics === "true" || false
  );
  const [questionsForAnalytics, setQuestionsForAnalytics] = useState([]);
  const [selectedQuestionForAnalytics, setSelectedQuestionForAnalytics] =
    useState();

  const [analyticsData, setAnalyticsData] = useState();
  const [
    previousSelectedQuestionForAnalytics,
    setPreviousSelectedQuestionForAnalytics,
  ] = useState();
  const [chartType, setChartType] = useState("bar");
  const [update9Box, setUpdate9Box] = useState(false);

  // state for edit question dropdown
  const [
    isManageQuestionsDropdownVisible,
    setIsManageQuestionsDropdownVisible,
  ] = useState(false);

  const valueTypeConfig = [
    {
      value: "discrete",
      label: (
        <Tooltip
          title="Score with rounded values (e.g. 1, 2, 3, 4, 5)"
          placement="top"
        >
          Rounded
        </Tooltip>
      ),
    },
    {
      value: "continuous",
      label: (
        <Tooltip
          title="Score with decimal ranges (e.g. 1.0-2.0, 2.1-3.0, 3.1-4.0, 4.1-5.0)"
          placement="top"
        >
          Decimal Ranges
        </Tooltip>
      ),
    },
  ];

  // state for value type
  const [valueType, setValueType] = useState("discrete");

  // hooks
  const params = useParams();
  const dispatch = useDispatch();
  const debouncedSearchQuery = useDebounce(
    searchValue,
    searchValue === "" ? 0 : 700
  );

  const getScoreCellColorClass = useCallback((score) => {
    if (score === 5 || score > 5) {
      return "score-exceptional";
    } else if (score >= 4 && score < 5) {
      return "score-highly-effective";
    } else if (score >= 3 && score < 4) {
      return "score-effective";
    } else if (score >= 2 && score < 4) {
      return "score-minimally-effective";
    } else if (score >= 1 && score < 2) {
      return "score-not-effective";
    } else return "";
  }, []);

  const getSortToolTipText = useCallback(
    (sortColumnId, subColumn = null) => {
      if (sortColumnId === columnForSorting && subColumn) {
        if (subColumn === subColumnForSorting) {
          if (sortOrder === "asc") return "Click to sort by descending order";
          else if (sortOrder === "no-sort")
            return "Click to sort by ascending order";
          else return "Click to remove sort";
        } else return "Click to sort by ascending order";
      } else if (sortColumnId === columnForSorting) {
        if (sortOrder === "asc") return "Click to sort by descending order";
        else if (sortOrder === "no-sort")
          return "Click to sort by ascending order";
        else return "Click to remove sort";
      } else return "Click to sort by ascending order";
    },
    [sortOrder, columnForSorting, subColumnForSorting]
  );

  const setSortSettings = useCallback(
    (columnId, subColumn = null) => {
      setColumnForSorting(columnId);
      setSortOrder((prevState) =>
        prevState === "no-sort"
          ? "asc"
          : prevState === "asc"
          ? "desc"
          : "no-sort"
      );
      setSubColumnForSorting(subColumn);
    },
    [setColumnForSorting, setSortOrder, sortOrder, setSubColumnForSorting]
  );

  const staticColumnSetup = useMemo(() => {
    return [
      {
        dataIndex: "reviewee",
        key: "revieweeImage",
        className: "calibration-table-fixed-reviewee-image-column-header",
        fixed: true,
        width: 100,

        render: (reviewee) => {
          return <RevieweeImageColumn reviewee={reviewee} />;
        },
      },
      {
        title: (
          <RevieweeColumnHeader
            columnForSorting={columnForSorting}
            setSortSettings={setSortSettings}
            getSortToolTipText={getSortToolTipText}
          />
        ),
        dataIndex: "reviewee",
        key: "reviewee",
        className: "calibration-table-fixed-reviewee-column-header",
        fixed: true,
        width: 300,

        render: (reviewee) => {
          return (
            <RevieweeColumnData
              reviewee={reviewee}
              setSelectedRevieweeForComment={setSelectedRevieweeForComment}
              setSelectedRevieweeForSummary={setSelectedRevieweeForSummary}
            />
          );
        },
      },
      {
        title: (
          <ManagerColumnHeader
            columnForSorting={columnForSorting}
            setSortSettings={setSortSettings}
            getSortToolTipText={getSortToolTipText}
          />
        ),
        dataIndex: "manager",
        key: "manager",
        className: "calibration-table-fixed-manager-column-header",
        fixed: false,
        width: 165,

        render: (manager) => {
          return <ManagerColumnData manager={manager} />;
        },
      },
    ];
  }, [columnForSorting, getSortToolTipText]);

  const fetchFilters = useCallback(async () => {
    try {
      const response = await WebService.get(
        `/accounts/${accountId}/review_cycle/${params.reviewCycleId}/reviewee_filters_calibration`
      );
      if (response.status === 200) {
        setFilterKeys(response.data?.reviewee_filters);
      } else {
        throw response;
      }
    } catch (error) {
      console.error(error);
    }
  }, [accountId, params, WebService, setFilterKeys]);

  const generateParamStringForCalibrationTable = useCallback(() => {
    let paramString = `page=${currentPage}&`;
    if (columnForSorting && sortOrder !== "no-sort") {
      paramString =
        paramString +
        `sort_column=${columnForSorting}&sort_direction=${sortOrder}&`;
    }
    if (subColumnForSorting && sortOrder !== "no-sort") {
      paramString = paramString + `sort_sub_column=${subColumnForSorting}&`;
    }

    if (debouncedSearchQuery.length) {
      paramString = paramString + `search_text=${debouncedSearchQuery}&`;
    }
    if (appliedFilters.length) {
      const requestData = {};
      appliedFilters.forEach((filter) => {
        if (requestData[filter.parent]) {
          requestData[filter.parent].push(
            filter.child?.id || filter.child?.name || filter.child
          );
        } else {
          requestData[filter.parent] = [
            filter.child?.id || filter.child?.name || filter.child,
          ];
        }
      });

      Object.keys(requestData).forEach((filter) => {
        paramString =
          paramString +
          `${filter}=${encodeURIComponent(
            JSON.stringify(requestData[filter])
          )}&`;
      });
    }
    return paramString;
  }, [
    currentPage,
    columnForSorting,
    sortOrder,
    subColumnForSorting,
    debouncedSearchQuery,
    appliedFilters,
  ]);

  const filterTable = useCallback(async () => {
    setTableLoading(true);
    const paramString = generateParamStringForCalibrationTable();
    if (paramString !== `page=${currentPage}&`) {
      try {
        const response = await WebService.get(
          `/accounts/${accountId}/review_cycle/${params.reviewCycleId}/calibration_table?${paramString}`
        );
        if (response.status === 200) {
          setCalibrationTableData(response.data);
          dispatch(getCalibrationTableData(params.reviewCycleId, paramString));
          setTableLoading(false);
        } else {
          throw response;
        }
      } catch (error) {
        console.error(error);
        setTableLoading(false);
        message.error("Unable to fetch filtered data, Please try again later");
      }
    } else {
      dispatch(getCalibrationTableData(params.reviewCycleId, paramString));
    }
  }, [
    generateParamStringForCalibrationTable,
    currentPage,
    accountId,
    params,
    setCalibrationTableData,
    setTableLoading,
    message,
    dispatch,
    getCalibrationTableData,
  ]);

  const fetchCSVConfig = () => {
    dispatch(
      fetchCSVBoxConfig("calibration_import", {
        review_cycle_id: params?.reviewCycleId,
      })
    );
  };

  // fetching filters and raw table data
  useEffect(() => {
    fetchFilters();

    setTableLoading(true);

    dispatch(getCalibratableQuestions(params.reviewCycleId));

    return () => {
      setShowCSVDropdown(false);
    };
  }, []);

  // csv box config
  useEffect(() => fetchCSVConfig, [params]);

  // selected question for analytics

  // setting up static columns and adding calibration table redux data to a state for local update of score changes
  useEffect(() => {
    if (selectedQuestionForAnalytics) {
      setPreviousSelectedQuestionForAnalytics(selectedQuestionForAnalytics);
    }
    fetchCalibrationAnalyticsQuestions();
    if (calibrationData) {
      setColumns(staticColumnSetup);
      setCalibrationTableData(calibrationData);
      setTableLoading(false);
    }
    if (selectedRevieweeForComment) {
      setSelectedRevieweeForComment(
        calibrationData.reviewees?.[selectedRevieweeForComment?.id]?.reviewee
      );
    }
    if (columnForSorting && document.getElementById(columnForSorting)) {
      const id = setTimeout(() => {
        document.getElementById(columnForSorting).scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
      }, 200);
      return () => clearTimeout(id);
    }
  }, [calibrationData]);

  // table loading and zero state, state changes
  useEffect(() => {
    if (!calibrationTableData) {
      return;
    }
    if (
      calibrationTableData?.reviewee_ids?.length === 0 &&
      !appliedFilters?.length &&
      !debouncedSearchQuery.length
    ) {
      setShowZeroState(true);
      setTableLoading(false);
    } else if (calibrationTableData?.columns === 0 && !appliedFilters?.length) {
      setShowZeroState(true);
      setNoQuestionZeroState(true);
      setTableLoading(false);
    } else {
      setShowZeroState(false);
      setNoQuestionZeroState(false);
      const data = [];
      calibrationTableData.reviewee_ids.forEach((revieweeId) => {
        data.push(calibrationTableData?.reviewees?.[revieweeId]);
      });

      setTableData([...data]);
    }
  }, [calibrationTableData, appliedFilters, debouncedSearchQuery]);

  // filtering table according to the applied filters by fetching the filtered table data and updating the state
  useEffect(() => {
    filterTable();
  }, [
    appliedFilters,
    currentPage,
    columnForSorting,
    subColumnForSorting,
    sortOrder,
    debouncedSearchQuery,
  ]);

  // updates the local table data on calibration score change
  const updateCalibrationScore = useCallback(
    ({
      revieweeId,
      updatedData,
      questionId,
      score,
      legend,
      isCustomQuestion = false,
    }) => {
      setCalibrationTableData((previousCalibrationData) => {
        const updatedCalibrationData = { ...previousCalibrationData };
        const questions = updatedCalibrationData.reviewees?.[revieweeId].rows;
        updatedData.forEach((item) => {
          if (!questions[item.calibration_column_id]) return;

          if (
            questions[item.calibration_column_id].values[
              isCustomQuestion ? 0 : 1
            ]
          ) {
            questions[item.calibration_column_id].values[
              isCustomQuestion ? 0 : 1
            ].value = item.calibrated_score;
            questions[item.calibration_column_id].values[
              isCustomQuestion ? 0 : 1
            ].description = item.calibrated_score_legend;
          }
        });

        const question = questions?.[questionId];

        if (question.values[isCustomQuestion ? 0 : 1]?.value !== score) {
          question.values[isCustomQuestion ? 0 : 1] = {
            value: score,
            description: legend,
          };
        }
        return updatedCalibrationData;
      });
      setUpdate9Box(true);
    },
    [calibrationTableData, setCalibrationTableData]
  );

  const paginationText = useMemo(() => {
    const pagination = calibrationTableData?.pagination;
    return `${
      pagination?.per_page * pagination?.current_page - pagination?.per_page + 1
    }-${
      pagination?.total_entries >
      pagination?.per_page * pagination?.current_page
        ? pagination?.per_page * pagination?.current_page
        : pagination?.total_entries
    } of ${pagination?.total_entries}`;
  }, [calibrationTableData]);

  const handleDownloadCSV = useCallback(async () => {
    const paramString = generateParamStringForCalibrationTable();
    try {
      const response = await WebService.get(
        `/accounts/${accountId}/review_cycle/${params.reviewCycleId}/calibration_table_csv?${paramString}`
      );
      if (response.status === 200) {
        notification.open({
          key: "calibration-csv",
          message: "You will receive the CSV via email once it's ready",
          duration: 5,
          icon: <CheckCircleOutlined style={{ color: "green" }} />,
        });
      } else {
        throw response;
      }
    } catch (error) {
      console.error(error);
    }
  }, [
    columnForSorting,
    subColumnForSorting,
    sortOrder,
    currentPage,
    appliedFilters,
    debouncedSearchQuery,
  ]);

  const handleNextPage = useCallback(() => {
    if (calibrationTableData?.pagination?.next_page)
      setCurrentPage(calibrationTableData?.pagination?.next_page);
  }, [calibrationTableData, setCurrentPage]);

  const handlePreviousPage = useCallback(() => {
    if (calibrationTableData?.pagination?.previous_page)
      setCurrentPage(calibrationTableData?.pagination?.previous_page);
  }, [calibrationTableData, setCurrentPage]);

  useEffect(() => {
    if (tableData.length) {
      const questionColumns = [];
      // const columnExists = (Id) => {
      //   return columns.some((col) => {
      //     return parseInt(col.key) === parseInt(Id);
      //   });
      // };
      calibrationTableData.columns.forEach((column) => {
        const columnObject = {
          title: (
            <QuestionColumnHeader
              column={column}
              columnForSorting={columnForSorting}
              setSortSettings={setSortSettings}
              getSortToolTipText={getSortToolTipText}
              subColumnForSorting={subColumnForSorting}
              setCalibrationTableData={setCalibrationTableData}
            />
          ),
          dataIndex: ["reviewee", "rows"],
          className: `${
            column?.visibility === "single" && !column.sub_columns?.length
              ? "calibration-table-additional-column"
              : column.visibility === "multiple"
              ? "calibration-table-moveable-column-parent"
              : "calibration-table-moveable-column-parent-small"
          } ${
            selectedCalibrationQuestion === column?.id &&
            showAnalytics &&
            chartType != null
              ? "selected-calibration-column-for-analytics"
              : ""
          } `,
          key: `${column?.id}`,
          fixed: false,
          width: 165,
        };
        if (column?.visibility === "single" && !column.sub_columns?.length) {
          columnObject.render = (text, row) => (
            <QuestionColumnData
              revieweeId={row?.reviewee?.id}
              rows={row?.rows}
              column={column}
              editable={column?.editable}
              handleCustomColumnScoreChange={handleCustomColumnScoreChange}
              getScoreCellColorClass={getScoreCellColorClass}
              isConfidential={row?.reviewee?.confidential}
            />
          );
        } else {
          columnObject.children = [];

          columnObject.children.push({
            title: "",
            className: `calibration-table-moveable-column-child`,
            dataIndex: "rows",
            key: `${column.id + "score"}`,
            width: 165,

            render: (rows, { reviewee }) => {
              return (
                <ResponseBox
                  getScoreCellColorClass={getScoreCellColorClass}
                  column={column}
                  rows={rows}
                  reviewee={reviewee}
                />
              );
            },
          });

          if (column.sub_columns?.length > 1)
            columnObject.children.push({
              title: "",
              className: "calibration-table-moveable-column-child",
              dataIndex: ["reviewee", "rows"],
              key: `${column.id + "calibrated_score"}`,
              width: 165,

              render: (text, row) => {
                if (
                  row?.rows[column.id] &&
                  (row?.rows[column.id].values?.[1]?.value || column.editable)
                )
                  return (
                    <CalibrationScoreCell
                      revieweeId={row?.reviewee?.id}
                      responseId={row?.rows[column.id].entity_id}
                      questionId={column.id}
                      score={row?.rows[column.id].values?.[1]?.value}
                      updateCalibrationScore={updateCalibrationScore}
                      getScoreCellColorClass={getScoreCellColorClass}
                      isReadOnly={!column.editable}
                    />
                  );
                else if (row?.reviewee?.confidential) {
                  return <ConfidentialScoreDiv />;
                } else {
                  return <NotEvaluatedDiv />;
                }
              },
            });
        }

        questionColumns.push({ ...columnObject });
      });

      setColumns([...staticColumnSetup, ...questionColumns]);
    }
  }, [tableData, selectedCalibrationQuestion, showAnalytics, chartType]);

  const isPreviousQuestionStillPresent = useMemo(() => {
    if (previousSelectedQuestionForAnalytics) {
      Object.keys(questionsForAnalytics).forEach((key) => {
        if (
          questionsForAnalytics[key].find(
            (question) => question.id === previousSelectedQuestionForAnalytics
          )
        ) {
          return true;
        }
      });
      return false;
    } else return false;
  }, [previousSelectedQuestionForAnalytics, questionsForAnalytics]);

  useEffect(() => {
    if (Object.keys(questionsForAnalytics).length) {
      if (isPreviousQuestionStillPresent) {
        setSelectedQuestionForAnalytics(previousSelectedQuestionForAnalytics);
        dispatch(
          setSelectedCalibrationQuestion(previousSelectedQuestionForAnalytics)
        );
      } else {
        setSelectedQuestionForAnalytics(
          questionsForAnalytics[Object.keys(questionsForAnalytics)[0]][0]?.id
        );

        dispatch(
          setSelectedCalibrationQuestion(
            questionsForAnalytics[Object.keys(questionsForAnalytics)[0]][0]?.id
          )
        );
      }
    }
  }, [questionsForAnalytics]);

  useEffect(() => {
    if (selectedQuestionForAnalytics && valueType && showAnalytics) {
      fetchGraphData();
    }
  }, [calibrationTableData, showAnalytics]);

  const fetchGraphData = async (selectedQuestionId, valType) => {
    try {
      let paramString = "&";
      if (debouncedSearchQuery.length) {
        paramString = paramString + `search_text=${debouncedSearchQuery}&`;
      }
      if (appliedFilters.length) {
        const requestData = {};
        appliedFilters.forEach((filter) => {
          if (requestData[filter.parent]) {
            requestData[filter.parent].push(
              filter.child?.id || filter.child?.name || filter.child
            );
          } else {
            requestData[filter.parent] = [
              filter.child?.id || filter.child?.name || filter.child,
            ];
          }
        });

        Object.keys(requestData).forEach((filter) => {
          paramString =
            paramString +
            `${filter}=${encodeURIComponent(
              JSON.stringify(requestData[filter])
            )}&`;
        });
      }
      const response = await WebService.get(
        `/accounts/${accountId}/review_cycle/${
          params.reviewCycleId
        }/calibration_charts?calibration_question_id=${
          selectedQuestionId || selectedQuestionForAnalytics
        }&value_type=${valType || valueType}${paramString}`
      );
      if (response.status === 200) {
        setAnalyticsData(response.data?.calibration_chart_data);
      } else throw response;
    } catch (error) {}
  };
  const handleCustomColumnScoreChange = useCallback(
    async (
      value,
      defaultValue,
      setInputValue,
      questionId,
      revieweeId,
      responseId = null,
      isText
    ) => {
      try {
        const newValue = value === "" ? null : value;

        const response = await WebService.post(
          `/accounts/${accountId}/review_cycle/${params.reviewCycleId}/reviewees/${revieweeId}/update_calibrated_response`,
          {
            response_id: responseId,
            calibration_column_id: questionId,
            ...(isText && { calibrated_response_text: newValue }),
            ...(!isText && {
              calibrated_score:
                newValue === null
                  ? null
                  : Math.round(parseFloat(newValue) * 10) / 10,
            }),
          }
        );

        if (response.status === 200) {
          message.success(
            `${isText ? "Response" : "Score"} updated successfully`
          );

          setCalibrationTableData((previousCalibrationData) => {
            const updatedCalibrationData = { ...previousCalibrationData };
            const questions =
              updatedCalibrationData.reviewees?.[revieweeId].rows;

            const question = questions?.[questionId];
            const isValueChanged = isText
              ? question.values[0]?.value !== newValue
              : question.values[0]?.value !==
                (newValue === null
                  ? null
                  : Math.round(parseFloat(newValue) * 10) / 10);

            if (isValueChanged) {
              const updatedValue = {
                value: isText
                  ? response.data.calibrated_response_plain_text
                  : newValue === null
                  ? null
                  : Math.round(parseFloat(newValue) * 10) / 10,
                description: "",
                html_content: response.data.calibrated_response_text,
              };
              question.values[0] = updatedValue;
            }

            return updatedCalibrationData;
          });

          setUpdate9Box(true);
        } else {
          throw response;
        }
      } catch (error) {
        const errorMessages = error?.response?.data?.error?.message;
        if (errorMessages?.length) {
          errorMessages.forEach((messageText) => {
            message.error(messageText);
          });
        }
        setInputValue(defaultValue);
        console.error(error);
      }
    },
    [accountId, params, message]
  );

  const fetchCalibrationAnalyticsQuestions = useCallback(async () => {
    try {
      const response = await WebService.get(
        `/accounts/${accountId}/review_cycle/${params.reviewCycleId}/calibration_charts/question_list`
      );
      if (response.status === 200) {
        setQuestionsForAnalytics(
          response.data?.review_cycle_calibration_questions
        );
      } else throw response;
    } catch (error) {
      console.error(error);
    }
  }, [WebService, accountId, params]);

  const handleImportCSVSuccess = useCallback(() => {
    filterTable();
  }, [filterTable, params, dispatch]);

  useEffect(() => {
    localStorage.setItem("showCalibrationAnalytics", showAnalytics);
  }, [showAnalytics]);

  const handleValueTypeSelection = (selectedQuestionId, val) => {
    setValueType(val);

    fetchGraphData(selectedQuestionId, val);
  };

  return {
    showZeroState,
    noQuestionZeroState,
    searchValue,
    setSearchValue,
    filterKeys,
    appliedFilters,
    debouncedSearchQuery,
    setAppliedFilters,
    handleDownloadCSV,
    isManageQuestionsDropdownVisible,
    setIsManageQuestionsDropdownVisible,
    tableLoading,
    columns,
    tableData,
    calibrationTableData,
    paginationText,
    handleNextPage,
    handlePreviousPage,
    selectedRevieweeForComment,
    generateParamStringForCalibrationTable,
    setSelectedRevieweeForComment,
    selectedRevieweeForSummary,
    calibrationData,
    setSelectedRevieweeForSummary,
    showAnalytics,
    setShowAnalytics,
    questionsForAnalytics,
    selectedQuestionForAnalytics,
    setSelectedQuestionForAnalytics,
    analyticsData,
    showCSVDropdown,
    setShowCSVDropdown,
    csvBoxConfig,
    showLoaderForCSVBox,
    setShowLoaderForCSVBox,
    handleImportCSVSuccess,
    update9Box,
    setUpdate9Box,
    chartType,
    setChartType,
    fetchCSVConfig,

    // Value type props
    valueTypeConfig,
    valueType,

    handleValueTypeSelection,
  };
};

export default useCalibrationTab;
